{!! csrf_field() !!}
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            Thông tin danh mục
        </h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="platform_id" class="form-label required">Nền tảng</label>
                    <select
                        name="platform_id"
                        id="platform_id"
                        class="form-select @error('platform_id') is-invalid @enderror"
                    >

                        @foreach($platforms as $item)
                            <option value="{{ $item->id }}" @selected(old('platform_id', $dataEdit->platform_id) == $item->id)>
                                {{ $item->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('platform_id')
                    <span class="invalid-feedback" role="alert">
                <strong>{{ $message }}</strong>
            </span>
                    @enderror
                </div>
            </div>

            <div class="col-md-12">
                <div class="form-group">
                    <label for="name" class="form-label required">Tên danh mục</label>
                    <input type="text" class="form-control" id="name" name="name"
                           value="{{ old('name', $dataEdit->name ?? null) }}">
                    @error('name')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="key" class="form-label required">Key</label>
                    <input type="text" class="form-control" id="key" name="key"
                           value="{{ old('key', $dataEdit->key ?? null) }}">
                    @error('key')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="description" class="form-label">Mô tả ngắn</label>
                    <textarea class="form-control" id="description" name="description"
                              rows="3">{{ old('description', $dataEdit->description ?? null) }}</textarea>
                    @error('description')
                    <span class="invalid-feedback" role="alert">
                        <strong>{{ $message }}</strong>
                    </span>
                    @enderror
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label for="status-category" class="form-label required">Trạng thái</label>
                    <select name="status" class="form-select" id="status-package">
                        @foreach(App\Enums\BaseStatusEnum::cases() as $status)
                            <option value="{{ $status->value }}"
                                @selected(old('status', $dataEdit->status->value ?? null) == $status->value)>
                                {{ $status->label() }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        <button type="submit" class="btn btn-success fw-medium">
            <i class="ri-save-line"></i>
            {{$form_title}}
        </button>
    </div>
</div>
