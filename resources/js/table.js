import moment from "moment";
var currency = 'vnd'
if ($.fn.dataTable) {
    $.extend($.fn.dataTable.defaults, {
        fnDrawCallback: function () {
            showToolTip();
        },
        "language": {
            "sProcessing": "Đang xử lý...",
            "sLengthMenu": "Xem _MENU_ mục",
            "emptyTable": "Không tìm thấy dòng nào phù hợp",
            "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
            "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
            "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
            "sInfoFiltered": "(được lọc từ _MAX_ mục)",
            "sInfoPostFix": "",
            "sSearch": "Tìm:",
            "sUrl": "",
            "oPaginate": {
                "sFirst": "Đầu",
                "sPrevious": "Trước",
                "sNext": "Tiếp",
                "sLast": "Cuối"
            }
        }
    });
}

const formatMoney = (input, suffix = '', roundNumber = true) => {
    if (!suffix) suffix = '';
    if (suffix == 'd') suffix = '₫';

    if (currency === 'usd') {
        roundNumber = false;
        if (suffix === '₫') suffix = '$';
        if (suffix.match(/vnd|VNĐ/i)) suffix = suffix.replace(/vnd|VNĐ/i, 'USD')
    } else if (currency === 'baht') {
        roundNumber = false;
        if (suffix === '₫') suffix = '฿';
        if (suffix.match(/vnd|VNĐ/i)) suffix = suffix.replace(/vnd|VNĐ/i, 'Baht')
    }

    var number = parseFloat(input);
    if (roundNumber) number = Math.floor(number);
    if (isNaN(number)) return input;

    if (currency === 'usd') number = round(number, 6);
    if (currency === 'baht') number = round(number, 4);

    // If float
    if (number && number.toString().includes('.')) {
        let parts = number.toString().split('.');
        return formatMoney(parts[0]) + '.' + parts[1] + suffix;
    }
    else {
        return number.toLocaleString() + suffix;
    }
}
export const components = {
    default: function (text) {
        return `<span class="text-success">${text}</span>`;
    },
    btn_edit: function (row, prefix = '', className = 'btn-edit') {
        return `<a href="${prefix}/${row.id}/edit" data-id="${row.id}" class="me-2 ${className}" title="Sửa">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="icon">
          <path d="M21.731 2.269a2.625 2.625 0 0 0-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 0 0 0-3.712ZM19.513 8.199l-3.712-3.712-8.4 8.4a5.25 5.25 0 0 0-1.32 2.214l-.8 2.685a.75.75 0 0 0 .933.933l2.685-.8a5.25 5.25 0 0 0 2.214-1.32l8.4-8.4Z" />
          <path d="M5.25 5.25a3 3 0 0 0-3 3v10.5a3 3 0 0 0 3 3h10.5a3 3 0 0 0 3-3V13.5a.75.75 0 0 0-1.5 0v5.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5V8.25a1.5 1.5 0 0 1 1.5-1.5h5.25a.75.75 0 0 0 0-1.5H5.25Z" />
        </svg>

            </a>`
    },
    btn_delete: function (row, prefix = '', className = 'btn-delete') {
        return `<a href="javascript:void(0)" data-id="${row.id}" data-url="${prefix}/${row.id}" class="me-2 ${className}" title="Xóa">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="icon text-danger">
                  <path fill-rule="evenodd" d="M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z" clip-rule="evenodd" />
                </svg>
            </a>`
    },
    checkbox: function (row) {
        return `<input type="checkbox" class="form-check-input row-checkbox" value="${row.id}" data-id="${row.id}">`;
    },
    checkbox_header: function () {
        return `<input type="checkbox" class="form-check-input" id="select-all-checkbox">`;
    },
    badge_success: (text) => `<span class="badge badge-success">${text}</span>`,
    badge_primary: (text) => `<span class="badge badge-primary">${text}</span>`,
    badge_warning: (text) => `<span class="badge badge-warning">${text}</span>`,
    badge_danger: (text) => `<span class="badge badge-danger">${text}</span>`,

    text_copyable: function (text) {
        return `<span class="copy-on-click text-success" data-title="Sao chép"
              data-toggle="tooltip" data-content="${text}">
              <i class="ri-file-copy-line"></i>
              ${text}
            </span>`;
    },

    table: {
        id: function (data, typeT, full) {
            return `<span class="text-success text-bold id-${full.id}">${data}</span>`;
        },
        url: function (data, type, full) {
            const displayText = data.length > 30 ? data.substring(0, 28) + '...' : data;
            return `<a href="${data}" class="text-plain" target="_blank">${displayText}</a>`;
        },

        title: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        name: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        username: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        value: function (data) {
            return `<span class="text-plain">${formatMoney(data,"")}</span>`;
        },
        platform_name: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        category: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        content: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        description: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        notes: function (data) {
            return `<span class="text-plain">${data ? data : ''}</span>`;
        },
        order_code: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        admin_note: function (data, typeT, full) {
            return `<span class="text-plain admin-note-${full.id}">${data ? data : ''}</span>`;
        },
        note: function (data, typeT, full) {
            return `<div class="text-plain note-${full.id}">${data ? data : ''}</div>`;
        },
        key: function (data) {
            return `<span class="badge bg-purple">${data}</span>`;
        },
        count: function (data) {
            return `<span class="badge bg-purple">${data}</span>`;
        },
        text_success: function (data) {
            return `<span class="text-success">${data}</span>`;
        },
        text_primary: function (data) {
            return `<span class="text-primary">${data}</span>`;
        },
        text_warning: function (data) {
            return `<span class="text-warning">${data}</span>`;
        },
        text_danger: function (data) {
            return `<span class="text-danger">${data}</span>`;
        },
        text_info: function (data) {
            return `<span class="text-info">${data}</span>`;
        },
        time: function (data) {
            if (!data) return ' ';
            return `<span class="badge bg-success">${moment(data).format('HH:mm:ss DD/MM/YYYY')}</span>`;
        },
        status: function (data, typeT, full) {
            return getStatusHtml(full);
        },
        type: function (data, typeT, full) {
            return getTypeHtml(full);
        },
        balance: function (data) {
            return `<span class="text-danger text-bold">${formatMoney(data, "")}</span>`;
        },
        member_price: function (data) {
            return `<span class="badge bg-purple">${formatMoney(data, "")}</span>`;
        },
        collaborator_price: function (data) {
            return `<span class="badge bg-orange">${formatMoney(data, "")}</span>`;
        },
        agency_price: function (data) {
            return `<span class="badge bg-pink">${formatMoney(data, "")}</span>`;
        },
        distributor_price: function (data) {
            return `<span class="badge bg-primary">${formatMoney(data, "")}</span>`;
        },
        price: function (data, type, row) {
            if (!row) return '';
            return `<span class="badge badge-sm bg-success">${formatMoney(row.balance_before || 0)}</span>
            <span>${row.math || '+'}</span>
            <span class="badge badge-sm bg-pink">${formatMoney(row.amount || 0)}</span>
            <span>=</span>
            <span class="badge badge-sm bg-primary money-value">${formatMoney(row.balance_after || 0)}</span>`;
        },
        price_per: function (data) {
            return `<span class="badge bg-success">${formatMoney(data, "")}</span>`;
        },
        total_payment: function (data) {
            return `<span class="badge bg-pink">${formatMoney(data, "")}</span>`;
        },
        order_status: function (data, dataT, full) {
            return getOrderStatusHtml(full);
        },
        tenant: function (data) {
            return `<span class="badge bg-purple">${data}</span>`;
        },
        checkbox: function (data, type, row) {
            return `<input type="checkbox" class="form-check-input row-checkbox" value="${row.id}" data-id="${row.id}">`;
        },
    }
}
function getStatusHtml(row) {
    const status = row.status;
    const statusMap = {
        1: { class: 'bg-success', text: 'Hoạt động' },
        0: { class: 'bg-danger', text: 'Không hoạt động' },
    };

    const statusConfig = statusMap[status] || { class: 'bg-info', text: status };
    return `<span class="badge ${statusConfig.class}">${statusConfig.text}</span>`;
}
function getOrderStatusHtml(row) {
    const orderStatus = row.order_status;
    const orderStatusMap = {
        Processing: { class: 'bg-purple', text: 'Đang xử lý' },
        Pending: { class: 'bg-warning', text: 'Chờ xử lý' },
        Completed: { class: 'bg-success', text: 'Hoàn thành' },
        Cancelled: { class: 'bg-danger', text: 'Đã hủy' },
        In_progress: { class: 'bg-primary', text: 'Đang chạy' },
        Refunded: { class: 'bg-pink', text: 'Hoàn tiền' },
        Waiting_cancel: { class: 'bg-warning', text: 'Đang chờ hủy' }
    };

    const orderStatusConfig = orderStatusMap[orderStatus] || { class: 'bg-info', text: orderStatus };
    return `<span class="badge ${orderStatusConfig.class}">${orderStatusConfig.text}</span>`;
}
function getTypeHtml(row) {
    const type = row.type;
    const typeMap = {
        plus_money: { class: 'bg-purple', text: 'Cộng tiền' },
        minus_money: { class: 'bg-warning', text: 'Trừ tiền' },
        payment: { class: 'bg-success', text: 'Nạp tiền' },
        deposit: { class: 'bg-purple', text: 'Mua dịch vụ' },
        percent: { class: 'bg-purple', text: 'Phần trăm' },
        fixed: { class: 'bg-primary', text: 'Cố định' },
    };

    const typeConfig = typeMap[type] || { class: 'bg-info', text: type };
    return `<span class="badge ${typeConfig.class}">${typeConfig.text}</span>`;
}
export const makeColumn = (title, name, render = null, disableSort = false) => {
    const obj = {
        title: title,
        data: name,
        name: name
    };

    if (disableSort) {
        Object.assign(obj, { orderable: false, searchable: false });
    }

    if (!render) render = name;

    if (render) {
        if (typeof render === 'string') {
            if (render === 'random') {
                const values = ['text_success', 'text_primary', 'text_warning', 'text_danger', 'text_info'];
                const randomIndex = Math.floor(Math.random() * values.length);
                obj.render = components.table[values[randomIndex]];
            }
            else if (typeof components[render] !== "undefined") {
                obj.render = components[render];
            } else if (typeof components.table[render] !== "undefined") {
                obj.render = components.table[render];
            }
            else {
                obj.render = components.table.content;
            }
        } else {
            obj.render = render;
        }
    }

    return obj;
}

/**
 * Re mapping the request url of datatable
 * @param url
 * @param callbackData
 */

var ajaxUrl,
    datatableVip,
    datatableLog;

/**
 * Re mapping the request url of datatable
 * @param url
 * @param callbackData
 */
export const xAjax = (url, callbackData = null) => {
    if (!ajaxUrl) ajaxUrl = url;
    return {
        url: url,
        data: function (data) {
            try {
                let order = data.order[0];
                order.field = data.columns[order.column].data;
                data.order_by = order.field;
                data.order_dir = order.dir;
                data.keyword = data.search.value;
                data.order = [];
                data.columns = [];

                if (typeof callbackData == "function") callbackData(data);

                return data;
            } catch (e) {
                console.log(e);
                return data;
            }
        }
    }
}
export const showToolTip = () => {
    $('[data-toggle="tooltip"], .btn-icon').tooltip();
}

export const reloadTable = {
    datatableVip: null,
    datatableLog: null,

    reload: () => {
        if (reloadTable.datatableVip) reloadTable.datatableVip.ajax.reload(showToolTip, false);
        if (reloadTable.datatableLog) reloadTable.datatableLog.ajax.reload(showToolTip, false);

        // Clear bulk selections after reload
        if (typeof bulkActions !== 'undefined') {
            bulkActions.selectedRows.clear();
            bulkActions.updateBulkActionVisibility();
            $('#select-all-checkbox').prop('checked', false);
        }
    }
}
export const definedColumns = {
    checkbox: {
        title: '<input type="checkbox" class="form-check-input" id="select-all-checkbox">',
        data: 'id',
        name: 'checkbox',
        orderable: false,
        searchable: false,
        render: function (data, type, row) {
            return `<input type="checkbox" class="form-check-input row-checkbox" value="${row.id}" data-id="${row.id}">`;
        }
    },
    stt: makeColumn('STT', 'id'),
    title: makeColumn('Tiêu đề', 'title'),
    name: makeColumn('Tên', 'name'),
    key: makeColumn('Key', 'key'),
    content: makeColumn('Nội dung', 'content'),
    created_at: makeColumn('Thời gian', 'created_at', 'time'),
    action: function (render) {
        return makeColumn('Hành Động', 'id', render, true);
    },
    status: makeColumn('Trạng thái', 'status', 'status'),
    platform_name: makeColumn('Nền tảng', 'platform_name', 'platform_name'),
    category: makeColumn('Danh mục', 'category', 'category'),
    balance: makeColumn('Số dư', 'balance', 'balance'),
    member_price: makeColumn('Giá thành viên', 'member_price', 'member_price'),
    collaborator_price: makeColumn('Giá CTV', 'collaborator_price', 'collaborator_price'),
    agency_price: makeColumn('Giá Đại lý', 'agency_price', 'agency_price'),
    distributor_price: makeColumn('Giá nhà phân phối', 'distributor_price', 'distributor_price'),
    price: makeColumn('Số tiền', 'amount', 'price'),
    type: makeColumn('Kiểu', 'type', 'type'),
    username: makeColumn('Thành viên', 'username', 'username'),
    tenant: makeColumn('Website', 'tenant', 'tenant'),
    code: makeColumn('Code', 'code', 'code'),
    description: makeColumn('Mô tả', 'description', 'description'),
    count: makeColumn('Số lượng', 'count', 'count'),
    price_per: makeColumn('Giá', 'price_per', 'price_per'),
    total_payment: makeColumn('Tổng thanh toán', 'total_payment', 'total_payment'),
    order_status: makeColumn('Trạng thái', 'order_status', 'order_status'),
    notes: makeColumn('Ghi chú', 'notes', 'notes'),
    note: makeColumn('Ghi chú', 'note', 'note'),
    url: makeColumn('Đường dẫn', 'url', 'url'),
    admin_note: makeColumn('Admin ghi chú', 'admin_note', 'admin_note'),
    order_code: makeColumn('Mã đơn hàng', 'order_code', 'order_code'),
    package_name: makeColumn('Dịch vụ', 'package_name', 'package_name'),
    category_name: makeColumn('Danh mục', 'category_name', 'category_name'),
    start_date: makeColumn('Ngày bắt đầu', 'start_date', 'time'),
    end_date: makeColumn('Ngày kết thúc', 'end_date', 'time'),
    value: makeColumn('Giá trị', 'value', 'value'),
}

// Bulk Actions functionality
export const bulkActions = {
    selectedRows: new Set(),

    init: function(tableSelector = '#datatable-ajax') {
        this.initSelectAll(tableSelector);
        this.initRowSelection(tableSelector);
        this.initBulkActionButtons();
        this.updateBulkActionVisibility();
    },

    initSelectAll: function(tableSelector) {
        $(document).on('change', '#select-all-checkbox', (e) => {
            const isChecked = e.target.checked;
            $(tableSelector + ' .row-checkbox').prop('checked', isChecked);

            if (isChecked) {
                $(tableSelector + ' .row-checkbox').each((index, checkbox) => {
                    this.selectedRows.add($(checkbox).val());
                });
            } else {
                this.selectedRows.clear();
            }

            this.updateBulkActionVisibility();
        });
    },

    initRowSelection: function(tableSelector) {
        $(document).on('change', '.row-checkbox', (e) => {
            const rowId = $(e.target).val();
            const isChecked = e.target.checked;

            if (isChecked) {
                this.selectedRows.add(rowId);
            } else {
                this.selectedRows.delete(rowId);
            }

            const totalCheckboxes = $(tableSelector + ' .row-checkbox').length;
            const checkedCheckboxes = $(tableSelector + ' .row-checkbox:checked').length;

            $('#select-all-checkbox').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
            $('#select-all-checkbox').prop('checked', checkedCheckboxes === totalCheckboxes);

            this.updateBulkActionVisibility();
        });
    },

    initBulkActionButtons: function() {
        $(document).on('click', '.bulk-action-btn', (e) => {
            e.preventDefault();
            const action = $(e.target).data('action');
            const selectedIds = Array.from(this.selectedRows);

            if (selectedIds.length === 0) {
                alert('Vui lòng chọn ít nhất một mục để thực hiện hành động.');
                return;
            }

            this.executeBulkAction(action, selectedIds);
        });
    },

    updateBulkActionVisibility: function() {
        const selectedCount = this.selectedRows.size;
        const bulkActionBar = $('.bulk-action-bar');
        const selectedCountSpan = $('.selected-count');

        if (selectedCount > 0) {
            bulkActionBar.removeClass('d-none');
            selectedCountSpan.text(selectedCount);
        } else {
            bulkActionBar.addClass('d-none');
        }
    },

    executeBulkAction: function(action, selectedIds) {
        const confirmMessage = this.getConfirmMessage(action, selectedIds.length);

        if (confirm(confirmMessage)) {
            const url = this.getBulkActionUrl(action);

            $.ajax({
                url: url,
                method: 'POST',
                data: {
                    ids: selectedIds,
                    action: action,
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: (response) => {
                    if (response.success) {
                        this.selectedRows.clear();
                        this.updateBulkActionVisibility();
                        reloadTable.reload();
                        alert(response.message || 'Thực hiện thành công!');
                    } else {
                        alert(response.message || 'Có lỗi xảy ra!');
                    }
                },
                error: (xhr) => {
                    alert('Có lỗi xảy ra khi thực hiện hành động!');
                    console.error(xhr);
                }
            });
        }
    },

    getConfirmMessage: function(action, count) {
        const messages = {
            'delete': `Bạn có chắc chắn muốn xóa ${count} mục đã chọn?`,
            'activate': `Bạn có chắc chắn muốn kích hoạt ${count} mục đã chọn?`,
            'deactivate': `Bạn có chắc chắn muốn vô hiệu hóa ${count} mục đã chọn?`,
            'export': `Bạn có muốn xuất ${count} mục đã chọn?`
        };

        return messages[action] || `Bạn có chắc chắn muốn thực hiện hành động này với ${count} mục đã chọn?`;
    },

    getBulkActionUrl: function(action) {
        const currentPath = window.location.pathname;
        return `${currentPath}/bulk-action`;
    }
};
