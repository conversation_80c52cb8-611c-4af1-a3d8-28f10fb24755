<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;

class TenantSetting extends Model
{
    protected $table = 'tenant_settings';

    protected $fillable = [
        'tenant_id',
        'key',
        'value'
    ];

    protected $casts = [
        'tenant_id' => 'integer',
    ];

    /**
     * Get the tenant that owns the setting
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Scope to filter settings by tenant
     */
    public function scopeForTenant($query, int $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    /**
     * Scope to filter settings by key
     */
    public function scopeByKey($query, string $key)
    {
        return $query->where('key', $key);
    }

    /**
     * Get the parsed value (attempts to decode JSON, falls back to string)
     */
    public function getParsedValueAttribute()
    {
        $decoded = json_decode($this->value, true);
        return json_last_error() === JSON_ERROR_NONE ? $decoded : $this->value;
    }

    /**
     * Set value (automatically encodes arrays/objects to JSON)
     */
    public function setValueAttribute($value)
    {
        $this->attributes['value'] = is_array($value) || is_object($value)
            ? json_encode($value)
            : $value;
    }

    /**
     * Get a setting value for a specific tenant and key
     */
    public static function getValue(int $tenantId, string $key, $default = null)
    {
        $cacheKey = "tenant_setting:{$tenantId}:{$key}";

        return Cache::remember($cacheKey, now()->addHours(6), function () use ($tenantId, $key, $default) {
            $setting = static::forTenant($tenantId)->byKey($key)->first();
            return $setting ? $setting->parsed_value : $default;
        });
    }

    /**
     * Set a setting value for a specific tenant and key
     */
    public static function setValue(int $tenantId, string $key, $value): self
    {
        $setting = static::updateOrCreate(
            ['tenant_id' => $tenantId, 'key' => $key],
            ['value' => $value]
        );

        // Clear cache
        Cache::forget("tenant_setting:{$tenantId}:{$key}");
        Cache::forget("tenant_settings:{$tenantId}");

        return $setting;
    }

    /**
     * Get all settings for a tenant as key-value pairs
     */
    public static function getAllForTenant(int $tenantId): array
    {
        $cacheKey = "tenant_settings:{$tenantId}";

        return Cache::remember($cacheKey, now()->addHours(6), function () use ($tenantId) {
            return static::forTenant($tenantId)
                ->get()
                ->pluck('parsed_value', 'key')
                ->toArray();
        });
    }

    /**
     * Clear cache when model is updated or deleted
     */
    protected static function booted(): void
    {
        static::saved(function ($setting) {
            Cache::forget("tenant_setting:{$setting->tenant_id}:{$setting->key}");
            Cache::forget("tenant_settings:{$setting->tenant_id}");
        });

        static::deleted(function ($setting) {
            Cache::forget("tenant_setting:{$setting->tenant_id}:{$setting->key}");
            Cache::forget("tenant_settings:{$setting->tenant_id}");
        });
    }
}
